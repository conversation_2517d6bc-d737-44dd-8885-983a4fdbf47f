# 🔐 Authentication Flow with Azure Container Apps Easy Auth

This document describes possibly the simplest way we can authenticate users with **Azure Container Apps Easy Auth** and how our **internal APIs** trust the initial Entra ID token without registering each API individually.

## 1. High-Level Flow

```mermaid
sequenceDiagram
    participant Browser
    participant ACA Easy Auth
    participant BFF Container
    participant Internal API

    Browser->>ACA Easy Auth: Request /login
    ACA Easy Auth->>Entra ID: Redirect to Microsoft login
    Entra ID-->>Browser: ID token (JWT) + Easy Auth cookie
    Browser->>BFF Container: Request with Easy Auth headers
    BFF Container->>Internal API: Forwards ID token in Authorization header
    Internal API->>Entra JWKS: Fetch signing keys (once, cached)
    Internal API-->>BFF Container: Validates JWT, extracts claims
```

**Key points**

* Easy Auth handles the **OIDC flow** (redirects, cookie/session).
* The **ID token** issued by Entra is made available to our app.
* The **BFF forwards that ID token** to our internal APIs.
* Internal APIs **verify Microsoft’s signature** and accept the token, trusting the original client (the BFF).

> *BFF = Backend For Frontend*

## 2. Azure Container App Setup

### Step 1 — Enable Easy Auth

1. In the **Azure Portal**, go to your Container App.
2. Under **Authentication**, click **Add identity provider**.
3. Choose **Microsoft** as the provider.
4. Either:

   * Let Azure create a new **App Registration**, or
   * Provide an existing **Client ID + Client Secret**.
5. Set **Restrict access** = *Require authentication*.

### Step 2 — Enable Token Store

* In the same **Authentication** blade, toggle on **Token Store**.
* This ensures tokens are cached and available to your app.

### Step 3 — Configure Scopes

* In the provider setup, request the scopes you need (usually `openid profile email` are enough since we only need an ID token).
* Save changes.

### Step 4 — App Service Headers

Once Easy Auth is active, requests to your container app will have extra headers:

* `X-MS-CLIENT-PRINCIPAL` → base64-encoded JSON with identity claims.
* `X-MS-TOKEN-AAD-ID-TOKEN` → the raw **JWT ID token** issued by Entra (if Token Store enabled).
* Alternatively, you can fetch all tokens via `GET /.auth/me`.

## 3. Forwarding the Token to Internal APIs

In your **BFF container**:

```js
// Example Express route
app.get("/proxy", async (req, res) => {
  const idToken = req.headers["x-ms-token-aad-id-token"];
  if (!idToken) return res.status(401).send("No ID token");

  // Forward to internal API
  const r = await fetch("http://internal-api.local/data", {
    headers: { Authorization: `Bearer ${idToken}` }
  });

  res.status(r.status).send(await r.text());
});
```

## 4. Internal API: Validating the ID Token

Each internal API **validates Microsoft’s signature** and some critical claims.

### Steps

1. Discover JWKS:
   `https://login.microsoftonline.com/<TENANT_ID>/v2.0/.well-known/openid-configuration` → `jwks_uri`
2. Fetch signing keys (cache them, handle rotation).
3. Validate:

   * Signature algorithm = RS256
   * `iss` = `https://login.microsoftonline.com/<TENANT_ID>/v2.0`
   * `aud` = the **BFF clientId** (the app registered in Entra)
   * `tid` = our tenant ID
   * `exp`/`nbf` = valid window
   * (Optional) `azp` = BFF clientId

### Node Example (using `jose`)

```js
import { jwtVerify, createRemoteJWKSet } from "jose";

const tenantId = "<TENANT_GUID>";
const bffClientId = "<BFF_CLIENT_ID>";
const issuer = `https://login.microsoftonline.com/${tenantId}/v2.0`;
const jwks = createRemoteJWKSet(new URL(`${issuer}/discovery/v2.0/keys`));

export async function validateIdToken(idToken) {
  const { payload } = await jwtVerify(idToken, jwks, {
    issuer,
    audience: bffClientId,
  });

  if (payload.tid !== tenantId) throw new Error("Unexpected tenant");
  if (payload.azp && payload.azp !== bffClientId) throw new Error("Unexpected azp");

  return {
    oid: payload.oid,
    name: payload.name,
    upn: payload.preferred_username,
    roles: payload.roles,
  };
}
```

## 5. Security Notes

* **ID token is meant for the client** (our BFF), not APIs. We’re making a **conscious trust decision** to reuse it for internal APIs.
* Enforce:

  * Internal APIs on **internal ingress only** (not internet-facing).
  * TLS inside the ACA environment (or private VNET).
  * Short ID token lifetimes (default \~1h). APIs should not refresh tokens.
* Consider auditing `azp` claim to ensure tokens were issued to the expected client.
* If you need stronger separation later, switch to **Option B** (BFF-minted JWTs) or **OBO access tokens**.

## 6. Example End-to-End Request

1. User visits the app in browser.
2. Easy Auth redirects to Microsoft login.
3. User authenticates → Entra returns ID token.
4. Easy Auth stores token, injects `X-MS-TOKEN-AAD-ID-TOKEN`.
5. BFF picks up token and forwards as `Authorization: Bearer <token>`.
6. Internal API validates signature + claims, extracts fields, and serves data.

# ACA Easy Auth (Microsoft Entra) — az CLI setup

## 0) Prereqs

```bash
# Make sure you’re on a recent az + containerapp module
az upgrade -y

# Log in & pick subscription
az login
az account set --subscription "<SUBSCRIPTION_ID>"
```

## 1) Vars

```bash
RG="<RESOURCE_GROUP>"
LOC="westeurope"                  # or your region
APP="<ACA_BFF_APP_NAME>"          # public ingress container app (the BFF)
TENANT="<TENANT_ID_GUID>"         # Entra tenant (GUID)
CLIENT_ID="<APP_REG_CLIENT_ID>"   # Entra app registration used by Easy Auth
CLIENT_SECRET="<APP_REG_SECRET>"  # client secret for that app registration

# Token store backing storage (recommended)
STG="<STORAGE_ACCOUNT_NAME>"
CONT="tokenstore"
BLOB_URI="https://${STG}.blob.core.windows.net/${CONT}"
```

## 2) (If needed) Create RG, app, and enable system-assigned identity

```bash
az group create -n "$RG" -l "$LOC"

# (If your Container App already exists, skip this block)
# NOTE: This is a minimal create; use your real image/environment as appropriate.
ENV="<ACA_ENV_NAME>"
IMAGE="mcr.microsoft.com/azuredocs/containerapps-helloworld:latest"
az containerapp env create -g "$RG" -n "$ENV" -l "$LOC"
az containerapp create -g "$RG" -n "$APP" \
  --environment "$ENV" \
  --image "$IMAGE" \
  --ingress external --target-port 80

# Assign a system-assigned managed identity (for token store access)
az containerapp identity assign -g "$RG" -n "$APP" --system-assigned
```

(Assigning MI via CLI: ([Microsoft Learn][1]))

## 3) Create the Token Store backing blob & grant access

```bash
# Storage account + container for token caching (/.auth/me etc.)
az storage account create -g "$RG" -n "$STG" -l "$LOC" --sku Standard_LRS
az storage container create --account-name "$STG" -n "$CONT"

# Get the principalId of the Container App's system-assigned identity
APP_MI_PRINCIPAL_ID=$(az containerapp identity show -g "$RG" -n "$APP" --query principalId -o tsv)

# Grant Blob Data Contributor on the storage account
az role assignment create \
  --assignee-object-id "$APP_MI_PRINCIPAL_ID" \
  --assignee-principal-type ServicePrincipal \
  --role "Storage Blob Data Contributor" \
  --scope "$(az storage account show -g "$RG" -n "$STG" --query id -o tsv)"
```

> Enabling the token store (blob-backed) is the supported way to expose tokens via `/.auth/me` and headers. CLI supports pointing the token store at your blob container and using the Container App’s managed identity to access it. ([Microsoft Learn][2])

## 4) Configure the Microsoft provider for Easy Auth

```bash
# Set issuer to the v2.0 issuer for your tenant
ISSUER="https://login.microsoftonline.com/${TENANT}/v2.0"

# Wire up the Microsoft provider (client id/secret, tenant, issuer)
az containerapp auth microsoft update \
  -g "$RG" -n "$APP" \
  --client-id "$CLIENT_ID" \
  --client-secret "$CLIENT_SECRET" \
  --tenant-id "$TENANT" \
  --issuer "$ISSUER" -y
```

(Provider wiring flags and example syntax from the official CLI docs. ([Microsoft Learn][3]))

> Using a client secret enables the hybrid/code flow so the platform can obtain and cache tokens (ID/access/refresh) for you. ([Microsoft Learn][4])

## 5) Turn on Easy Auth + Token Store and choose unauthenticated behavior

```bash
# Enable Easy Auth platform; enforce HTTPS; enable token store with MI auth
az containerapp auth update -g "$RG" -n "$APP" \
  --enabled true \
  --require-https true \
  --token-store true \
  --blob-container-uri "$BLOB_URI"

# OPTION A: Block anonymous with 401 (you link to /.auth/login/aad in your UI)
az containerapp auth update -g "$RG" -n "$APP" \
  --unauthenticated-client-action Return401

# OPTION B: Auto-redirect anonymous to Microsoft login page at the edge
# (You can also set --redirect-provider; provider redirect is documented in portal docs.
# If you prefer explicit linking, keep Return401 and link to /.auth/login/aad.)
# az containerapp auth update -g "$RG" -n "$APP" \
#   --unauthenticated-client-action RedirectToLoginPage
```

(Flags: `--enabled`, `--token-store`, `--blob-container-uri`, `--unauthenticated-client-action`, `--require-https` are documented on `az containerapp auth update`; examples for token store shown in CLI docs. Also, redirecting to `/.auth/login/<provider>` is described in the ACA auth article. ([Microsoft Learn][2]))

> You can always link to `/.auth/login/aad` yourself in the BFF (e.g., “Sign in with Microsoft”)—that path is standard for the Microsoft provider. ([Microsoft Learn][5])

## 6) Verify the configuration

```bash
# Show the app’s auth config
az containerapp auth show -g "$RG" -n "$APP" -o yaml
# Show the Microsoft provider config
az containerapp auth microsoft show -g "$RG" -n "$APP" -o yaml
```

(Commands referenced from CLI docs. ([Microsoft Learn][2]))

After you sign in through Easy Auth, you should see:

* Headers like `X-MS-CLIENT-PRINCIPAL` (claims) and, when token store is enabled and configured, **`X-MS-TOKEN-AAD-ID-TOKEN`** on your requests.
* `GET /.auth/me` returns a JSON payload with cached tokens for the signed-in user (ID/access/refresh where applicable). ([Microsoft Learn][2])

# Using “Option A”: BFF forwards the ID token to internal APIs

**Flow**

1. Browser hits BFF (public ingress).
2. Easy Auth challenges, stores tokens, and injects headers.
3. BFF reads the **ID token** and forwards it to internal APIs as `Authorization: Bearer <ID_TOKEN>`.
4. Internal APIs **validate Microsoft’s signature** + critical claims and extract fields.

### BFF (Node/Express) – forward the ID token

```js
app.use((req, res, next) => {
  // Easy Auth lower-cases header keys; handle both
  const idToken = req.headers["x-ms-token-aad-id-token"] || req.headers["X-MS-TOKEN-AAD-ID-TOKEN"];
  if (!idToken) return res.status(401).send("No ID token from Easy Auth");
  req.idToken = String(idToken);
  next();
});

app.get("/proxy/data", async (req, res) => {
  const r = await fetch("http://internal-api/data", {
    headers: { Authorization: `Bearer ${req.idToken}` }
  });
  res.status(r.status).send(await r.text());
});
```

### Internal API – validate the ID token (signature + claims)

```js
import { jwtVerify, createRemoteJWKSet } from "jose";

const TENANT = process.env.AZURE_TENANT_ID;       // same tenant you configured
const BFF_CLIENT_ID = process.env.BFF_CLIENT_ID;  // the Entra app used by Easy Auth
const ISSUER = `https://login.microsoftonline.com/${TENANT}/v2.0`;
const JWKS = createRemoteJWKSet(new URL(`${ISSUER}/discovery/v2.0/keys`));

export async function verifyIdToken(idToken) {
  const { payload } = await jwtVerify(idToken, JWKS, {
    issuer: ISSUER,
    audience: BFF_CLIENT_ID, // ID token is meant for the BFF client
  });

  if (payload.tid !== TENANT) throw new Error("Unexpected tenant");
  if (payload.azp && payload.azp !== BFF_CLIENT_ID) throw new Error("Unexpected authorized party");
  return payload; // oid, name, preferred_username, roles, groups (if present)
}
```

> The issuer/JWKS discovery pattern and required checks (`iss`, `aud`, `exp/nbf`, etc.) follow OpenID Connect & Microsoft identity guidance. For ACA auth behavior and provider setup, see the official docs. ([Microsoft Learn][2])

## Troubleshooting tips

* **I don’t see token headers, only `X-MS-CLIENT-PRINCIPAL`:**
  Ensure you configured the **Microsoft provider with a client secret** and **enabled token store**. Then check `/.auth/me` after sign-in. ([Microsoft Learn][3])

* **Want auto-redirect to login?**
  You can configure the unauthenticated action to **RedirectToLoginPage** and link to `/.auth/login/aad` in your UI. The provider routing convention is documented in ACA auth. ([Microsoft Learn][5])

* **Token store using MI:**
  Use `--token-store true --blob-container-uri ...` and ensure the Container App’s **system-assigned identity** has **Storage Blob Data Contributor** on the storage account. ([Microsoft Learn][2])

## Reference docs

* **az containerapp auth** (enable platform auth, token store, behavior): ([Microsoft Learn][2])
* **az containerapp auth microsoft update** (client id/secret, issuer, tenant): ([Microsoft Learn][3])
* **ACA authentication overview** (login endpoints, redirect behavior): ([Microsoft Learn][5])
* **Managed identity for Container Apps** (assign MI via CLI): ([Microsoft Learn][6])

[1]: https://learn.microsoft.com/en-us/cli/azure/containerapp/identity?view=azure-cli-latest&utm_source=chatgpt.com "az containerapp identity"
[2]: https://learn.microsoft.com/en-us/cli/azure/containerapp/auth?view=azure-cli-latest "az containerapp auth | Microsoft Learn"
[3]: https://learn.microsoft.com/en-us/cli/azure/containerapp/auth/microsoft?view=azure-cli-latest "az containerapp auth microsoft | Microsoft Learn"
[4]: https://learn.microsoft.com/en-us/azure/container-apps/authentication-entra?utm_source=chatgpt.com "Enable authentication and authorization in Azure ..."
[5]: https://learn.microsoft.com/en-us/azure/container-apps/authentication?utm_source=chatgpt.com "Authentication and authorization in Azure Container Apps"
[6]: https://learn.microsoft.com/en-us/azure/container-apps/managed-identity?utm_source=chatgpt.com "Managed identities in Azure Container Apps"
