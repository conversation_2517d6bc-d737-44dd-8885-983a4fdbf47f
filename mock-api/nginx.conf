server {
    listen 80;
    server_name localhost;
    root /usr/share/nginx/html;

    # Add CORS headers
    add_header Access-Control-Allow-Origin *;
    add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
    add_header Access-Control-Allow-Headers "Content-Type, Authorization";

    # Handle preflight requests
    location / {
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
            add_header Access-Control-Allow-Headers "Content-Type, Authorization";
            add_header Content-Length 0;
            add_header Content-Type text/plain;
            return 200;
        }
    }

    # Mock leads processing endpoint
    location /api/leads/process {
        default_type application/json;
        return 200 '{"status":"success","message":"Mock processing completed","recordsReceived":1,"processedAt":"2024-01-01T00:00:00Z"}';
    }

    # Health check
    location /health {
        return 200 '{"status":"healthy","service":"mock-leads-adapter-api"}';
    }
}
