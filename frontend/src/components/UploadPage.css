.upload-page {
  flex: 1;
  padding: 2rem;
  background-color: #f9fafb;
}

.upload-container {
  max-width: 800px;
  margin: 0 auto;
}

.upload-header {
  text-align: center;
  margin-bottom: 2rem;
}

.upload-header h2 {
  margin: 0 0 0.5rem 0;
  color: #1f2937;
  font-size: 1.8rem;
  font-weight: 600;
}

.upload-header p {
  margin: 0;
  color: #6b7280;
  font-size: 1rem;
}

.upload-methods {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.upload-section {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.upload-section h3 {
  margin: 0 0 1rem 0;
  color: #1f2937;
  font-size: 1.2rem;
  font-weight: 600;
}

/* File Upload Styles */
.dropzone {
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s;
  background-color: #fafafa;
}

.dropzone:hover:not(.disabled) {
  border-color: #0078d4;
  background-color: #f0f8ff;
}

.dropzone.active {
  border-color: #0078d4;
  background-color: #e6f3ff;
}

.dropzone.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.dropzone-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.upload-icon {
  color: #6b7280;
  margin-bottom: 0.5rem;
}

.dropzone p {
  margin: 0;
  color: #374151;
}

.file-types {
  font-size: 0.8rem;
  color: #6b7280;
}

/* Text Input Styles */
.text-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.text-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  resize: vertical;
  min-height: 200px;
}

.text-input:focus {
  outline: none;
  border-color: #0078d4;
  box-shadow: 0 0 0 3px rgba(0, 120, 212, 0.1);
}

.text-input:disabled {
  background-color: #f3f4f6;
  cursor: not-allowed;
}

.submit-button {
  background-color: #0078d4;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: background-color 0.2s;
}

.submit-button:hover:not(:disabled) {
  background-color: #106ebe;
}

.submit-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
  .upload-page {
    padding: 1rem;
  }
  
  .upload-methods {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .upload-section {
    padding: 1rem;
  }
  
  .dropzone {
    padding: 1.5rem;
  }
}
