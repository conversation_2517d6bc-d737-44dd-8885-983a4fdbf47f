# Deployment Guide

This guide covers deploying the Leads Intake App to Azure Container Apps with Easy Auth.

## Prerequisites

- Azure CLI installed and configured
- Docker installed (for building images)
- Azure subscription with appropriate permissions
- Azure Container Registry (ACR) or other container registry

## Azure Container Apps Deployment

### 1. Prepare Container Images

Build and push your container images to a registry:

```bash
# Build and tag images
docker build -t your-registry.azurecr.io/leads-intake-frontend:latest ./frontend
docker build -t your-registry.azurecr.io/leads-intake-backend:latest ./backend

# Push to registry
docker push your-registry.azurecr.io/leads-intake-frontend:latest
docker push your-registry.azurecr.io/leads-intake-backend:latest
```

### 2. Create Azure Resources

```bash
# Set variables
RESOURCE_GROUP="leads-intake-rg"
LOCATION="westeurope"
ENVIRONMENT="leads-intake-env"
FRONTEND_APP="leads-intake-frontend"
BACKEND_APP="leads-intake-backend"

# Create resource group
az group create --name $RESOURCE_GROUP --location $LOCATION

# Create Container Apps environment
az containerapp env create \
  --name $ENVIRONMENT \
  --resource-group $RESOURCE_GROUP \
  --location $LOCATION
```

### 3. Deploy Backend (BFF)

```bash
# Deploy backend container app
az containerapp create \
  --name $BACKEND_APP \
  --resource-group $RESOURCE_GROUP \
  --environment $ENVIRONMENT \
  --image your-registry.azurecr.io/leads-intake-backend:latest \
  --target-port 3001 \
  --ingress internal \
  --env-vars \
    NODE_ENV=production \
    PORT=3001 \
    AZURE_TENANT_ID=your-tenant-id \
    BFF_CLIENT_ID=your-client-id \
    LEADS_ADAPTER_API_URL=http://leads-adapter-api:8080
```

### 4. Deploy Frontend

```bash
# Deploy frontend container app
az containerapp create \
  --name $FRONTEND_APP \
  --resource-group $RESOURCE_GROUP \
  --environment $ENVIRONMENT \
  --image your-registry.azurecr.io/leads-intake-frontend:latest \
  --target-port 80 \
  --ingress external \
  --env-vars \
    VITE_API_URL=https://$BACKEND_APP.internal.domain
```

### 5. Configure Easy Auth

Follow the detailed instructions in `../ACA-Authentication.md` to set up Azure AD authentication.

Key steps:
1. Create Azure AD App Registration
2. Configure Easy Auth on the frontend container app
3. Set up token store with blob storage
4. Configure authentication middleware

### 6. Environment Variables

**Backend Environment Variables:**
```bash
NODE_ENV=production
PORT=3001
AZURE_TENANT_ID=your-tenant-id-guid
BFF_CLIENT_ID=your-app-registration-client-id
LEADS_ADAPTER_API_URL=http://leads-adapter-api:8080
FRONTEND_URL=https://your-frontend-domain.com
```

**Frontend Environment Variables:**
```bash
VITE_API_URL=https://your-backend-domain.com
```

## Security Considerations

1. **Network Security:**
   - Frontend: External ingress (internet-facing)
   - Backend: Internal ingress (only accessible within the environment)
   - Leads Adapter API: Internal only

2. **Authentication:**
   - All requests to backend require valid Azure AD tokens
   - Tokens are validated using Microsoft's JWKS
   - Short token lifetimes (1 hour default)

3. **HTTPS:**
   - All external traffic uses HTTPS
   - Internal traffic should use TLS where possible

## Monitoring and Logging

1. **Application Insights:**
   ```bash
   # Add Application Insights to your container apps
   az containerapp update \
     --name $BACKEND_APP \
     --resource-group $RESOURCE_GROUP \
     --set-env-vars APPLICATIONINSIGHTS_CONNECTION_STRING=your-connection-string
   ```

2. **Log Analytics:**
   - Container Apps automatically send logs to Log Analytics
   - Configure custom queries for monitoring

3. **Health Checks:**
   - Backend: `/health` endpoint
   - Frontend: `/health` endpoint (nginx)

## Scaling Configuration

```bash
# Configure scaling rules
az containerapp update \
  --name $BACKEND_APP \
  --resource-group $RESOURCE_GROUP \
  --min-replicas 1 \
  --max-replicas 10 \
  --scale-rule-name http-requests \
  --scale-rule-type http \
  --scale-rule-metadata concurrentRequests=100
```

## Troubleshooting

1. **Authentication Issues:**
   - Check Azure AD app registration configuration
   - Verify tenant ID and client ID
   - Check Easy Auth configuration
   - Review token validation logs

2. **Network Issues:**
   - Verify ingress configuration
   - Check internal DNS resolution
   - Review firewall rules

3. **Application Issues:**
   - Check container logs: `az containerapp logs show`
   - Verify environment variables
   - Test health endpoints

## Rollback Strategy

1. **Blue-Green Deployment:**
   - Deploy new version to staging environment
   - Test thoroughly
   - Switch traffic using revision management

2. **Revision Management:**
   ```bash
   # List revisions
   az containerapp revision list --name $BACKEND_APP --resource-group $RESOURCE_GROUP
   
   # Activate previous revision
   az containerapp revision activate --revision previous-revision-name
   ```
