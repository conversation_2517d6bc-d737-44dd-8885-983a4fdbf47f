import express from 'express';

const router = express.Router();

/**
 * Health check endpoint
 */
router.get('/', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'leads-intake-bff',
    version: process.env.npm_package_version || '1.0.0',
  });
});

/**
 * Readiness check endpoint
 */
router.get('/ready', (req, res) => {
  // Add any readiness checks here (database connections, etc.)
  res.json({
    status: 'ready',
    timestamp: new Date().toISOString(),
  });
});

export default router;
