import { checkSig<PERSON>rypt<PERSON><PERSON><PERSON> } from './crypto_key.js';
import invalidKeyInput from './invalid_key_input.js';
export default async (alg, key, usage) => {
    if (key instanceof Uint8Array) {
        if (!alg.startsWith('HS')) {
            throw new TypeError(invalidKeyInput(key, 'CryptoKey', 'KeyObject', 'JSON Web Key'));
        }
        return crypto.subtle.importKey('raw', key, { hash: `SHA-${alg.slice(-3)}`, name: 'HM<PERSON>' }, false, [usage]);
    }
    checkSigCryptoKey(key, alg, usage);
    return key;
};
