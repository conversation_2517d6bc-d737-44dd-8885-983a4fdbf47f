import { useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { uploadService } from '../services/uploadService';
import './UploadPage.css';

function UploadPage() {
  const [textInput, setTextInput] = useState('');
  const [isUploading, setIsUploading] = useState(false);
  const [uploadResult, setUploadResult] = useState(null);
  const [error, setError] = useState(null);

  const onDrop = async (acceptedFiles) => {
    if (acceptedFiles.length > 0) {
      const file = acceptedFiles[0];
      await handleFileUpload(file);
    }
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/plain': ['.txt'],
      'text/csv': ['.csv'],
      'application/json': ['.json'],
    },
    maxFiles: 1,
    maxSize: 10 * 1024 * 1024, // 10MB
  });

  const handleFileUpload = async (file) => {
    try {
      setIsUploading(true);
      setError(null);
      setUploadResult(null);

      const result = await uploadService.uploadFile(file);
      setUploadResult(result);
    } catch (err) {
      setError(err.message);
    } finally {
      setIsUploading(false);
    }
  };

  const handleTextSubmit = async (e) => {
    e.preventDefault();
    
    if (!textInput.trim()) {
      setError('Please enter some text to process');
      return;
    }

    try {
      setIsUploading(true);
      setError(null);
      setUploadResult(null);

      const result = await uploadService.uploadText(textInput);
      setUploadResult(result);
    } catch (err) {
      setError(err.message);
    } finally {
      setIsUploading(false);
    }
  };

  const clearResults = () => {
    setUploadResult(null);
    setError(null);
    setTextInput('');
  };

  return (
    <div className="upload-page">
      <div className="upload-container">
        <div className="upload-header">
          <h2>Process Leads Data</h2>
          <p>Upload a file or paste text to process leads and call-off requests</p>
        </div>

        {error && (
          <div className="error-message">
            <p>{error}</p>
            <button onClick={clearResults} className="clear-button">
              Clear
            </button>
          </div>
        )}

        {uploadResult && (
          <div className="success-message">
            <h3>✅ Processing Complete</h3>
            <p>Your data has been successfully processed and sent to the leads adapter.</p>
            <div className="result-details">
              <p><strong>Status:</strong> {uploadResult.status}</p>
              <p><strong>Records processed:</strong> {uploadResult.recordsProcessed || 'N/A'}</p>
              {uploadResult.message && <p><strong>Message:</strong> {uploadResult.message}</p>}
            </div>
            <button onClick={clearResults} className="clear-button">
              Process Another
            </button>
          </div>
        )}

        {!uploadResult && (
          <div className="upload-methods">
            {/* File Upload Section */}
            <div className="upload-section">
              <h3>Upload File</h3>
              <div 
                {...getRootProps()} 
                className={`dropzone ${isDragActive ? 'active' : ''} ${isUploading ? 'disabled' : ''}`}
              >
                <input {...getInputProps()} disabled={isUploading} />
                <div className="dropzone-content">
                  <svg className="upload-icon" viewBox="0 0 24 24" width="48" height="48">
                    <path fill="currentColor" d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
                  </svg>
                  {isDragActive ? (
                    <p>Drop the file here...</p>
                  ) : (
                    <>
                      <p>Drag and drop a file here, or click to select</p>
                      <p className="file-types">Supported: .txt, .csv, .json (max 10MB)</p>
                    </>
                  )}
                </div>
              </div>
            </div>

            {/* Text Input Section */}
            <div className="upload-section">
              <h3>Paste Text</h3>
              <form onSubmit={handleTextSubmit} className="text-form">
                <textarea
                  value={textInput}
                  onChange={(e) => setTextInput(e.target.value)}
                  placeholder="Paste your leads data here..."
                  className="text-input"
                  rows={10}
                  disabled={isUploading}
                />
                <button 
                  type="submit" 
                  disabled={isUploading || !textInput.trim()}
                  className="submit-button"
                >
                  {isUploading ? (
                    <>
                      <div className="spinner"></div>
                      Processing...
                    </>
                  ) : (
                    'Process Text'
                  )}
                </button>
              </form>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default UploadPage;
