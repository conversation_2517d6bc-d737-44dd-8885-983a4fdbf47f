export function assertCrypto<PERSON><PERSON>(key) {
    if (!isCrypto<PERSON>ey(key)) {
        throw new Error('CryptoKey instance expected');
    }
}
export function isCrypto<PERSON>ey(key) {
    return key?.[Symbol.toStringTag] === 'CryptoKey';
}
export function isKeyObject(key) {
    return key?.[Symbol.toStringTag] === 'KeyObject';
}
export default (key) => {
    return isCryptoKey(key) || isKeyObject(key);
};
