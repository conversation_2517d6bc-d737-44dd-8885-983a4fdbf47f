import express from 'express';
import { asyncHandler } from '../middleware/errorHandler.js';

const router = express.Router();

/**
 * Get current user information
 */
router.get('/me', asyncHandler(async (req, res) => {
  if (!req.user) {
    return res.status(401).json({ error: 'Not authenticated' });
  }

  res.json({
    user: req.user,
    authenticated: true,
  });
}));

/**
 * <PERSON>gin endpoint (redirects to Easy Auth)
 */
router.get('/login', (req, res) => {
  // In production with Easy Auth, redirect to /.auth/login/aad
  if (process.env.NODE_ENV === 'production') {
    res.redirect('/.auth/login/aad');
  } else {
    // In development, return mock login info
    res.json({
      message: 'Development mode - authentication bypassed',
      loginUrl: '/.auth/login/aad',
    });
  }
});

/**
 * Logout endpoint
 */
router.post('/logout', (req, res) => {
  // In production with Easy Auth, redirect to /.auth/logout
  if (process.env.NODE_ENV === 'production') {
    res.redirect('/.auth/logout');
  } else {
    res.json({ message: 'Logged out successfully' });
  }
});

export default router;
