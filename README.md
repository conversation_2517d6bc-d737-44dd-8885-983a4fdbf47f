# Leads Intake App

A modern web application for processing leads, call-off requests, and other data into Cinode and other integrations. This application serves as the starting point for the LoadLeadsAndNotifyWorkflow via the leads-adapter-api internal API.

## Architecture

- **Frontend**: React with Vite for modern, fast development
- **Backend**: Node.js/Express BFF (Backend for Frontend)
- **Authentication**: Azure Container Apps Easy Auth with Office 365/Azure AD
- **Deployment**: Docker containers for Azure Container Apps

## Features

- 📝 Text input (paste) and file drag-and-drop upload
- 🔐 Seamless Office 365 authentication
- 🚀 Modern React UI with responsive design
- 🔒 Secure JWT token handling and API proxying
- 📦 Docker-ready for container deployment

## Project Structure

```
leads-intake-app/
├── frontend/              # React application with Vite
│   ├── src/
│   │   ├── components/    # React components
│   │   ├── contexts/      # React contexts (Auth)
│   │   ├── services/      # API services
│   │   └── utils/         # Utility functions
│   ├── Dockerfile         # Frontend container
│   └── nginx.conf         # Nginx configuration
├── backend/               # Node.js BFF server
│   ├── src/
│   │   ├── middleware/    # Express middleware
│   │   ├── routes/        # API routes
│   │   ├── services/      # Business logic
│   │   └── utils/         # Utility functions
│   └── Dockerfile         # Backend container
├── mock-api/              # Mock leads-adapter-api for development
├── docker-compose.yml     # Local development setup
└── docs/                  # Documentation
```

## Quick Start

### Prerequisites

- Node.js 18+ and npm
- Docker and Docker Compose (for containerized development)

### Development Setup

1. **Clone and setup the project:**

   ```bash
   git clone <repository-url>
   cd leads-intake-app
   ```

2. **Option A: Local Development**

   ```bash
   # Install backend dependencies
   cd backend
   npm install
   cp .env.example .env

   # Install frontend dependencies
   cd ../frontend
   npm install
   cp .env.example .env

   # Start backend (in one terminal)
   cd ../backend
   npm run dev

   # Start frontend (in another terminal)
   cd ../frontend
   npm run dev
   ```

3. **Option B: Docker Development**

   ```bash
   # Start all services
   docker-compose up --build
   ```

4. **Access the application:**
   - Frontend: http://localhost:5173 (local) or http://localhost:3000 (Docker)
   - Backend API: http://localhost:3001
   - Mock Leads API: http://localhost:8080

## Authentication Setup

This application uses Azure Container Apps Easy Auth for authentication. For production deployment:

1. **Configure Azure AD App Registration:**

   - Create an App Registration in Azure AD
   - Note the Client ID and Tenant ID
   - Configure redirect URIs for your domain

2. **Set Environment Variables:**

   ```bash
   AZURE_TENANT_ID=your-tenant-id
   BFF_CLIENT_ID=your-client-id
   ```

3. **Configure Easy Auth:**
   Follow the detailed instructions in `ACA-Authentication.md`

## API Endpoints

### Authentication

- `GET /api/auth/me` - Get current user info
- `GET /api/auth/login` - Initiate login (redirects to Azure AD)
- `POST /api/auth/logout` - Logout user

### File Upload

- `POST /api/upload/file` - Upload and process file
- `POST /api/upload/text` - Process text input

### Health

- `GET /health` - Health check endpoint
