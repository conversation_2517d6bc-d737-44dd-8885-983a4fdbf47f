const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001';

class UploadService {
  async uploadFile(file) {
    const formData = new FormData();
    formData.append('file', file);

    const response = await fetch(`${API_BASE_URL}/api/upload/file`, {
      method: 'POST',
      credentials: 'include',
      body: formData,
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'File upload failed');
    }

    return await response.json();
  }

  async uploadText(text) {
    const response = await fetch(`${API_BASE_URL}/api/upload/text`, {
      method: 'POST',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ text }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Text upload failed');
    }

    return await response.json();
  }
}

export const uploadService = new UploadService();
