import { jwtVerify, createRemoteJWKSet } from 'jose';

// Azure AD configuration
const TENANT_ID = process.env.AZURE_TENANT_ID;
const BFF_CLIENT_ID = process.env.BFF_CLIENT_ID;
const ISSUER = `https://login.microsoftonline.com/${TENANT_ID}/v2.0`;
const JWKS = TENANT_ID ? createRemoteJWKSet(new URL(`${ISSUER}/discovery/v2.0/keys`)) : null;

/**
 * Validates Azure AD ID token from Easy Auth
 * @param {string} idToken - The JWT ID token
 * @returns {Object} - Validated user payload
 */
export async function validateIdToken(idToken) {
  if (!TENANT_ID || !BFF_CLIENT_ID) {
    throw new Error('Azure AD configuration missing');
  }

  try {
    const { payload } = await jwtVerify(idToken, JWKS, {
      issuer: ISSUER,
      audience: BFF_CLIENT_ID,
    });

    // Additional validation
    if (payload.tid !== TENANT_ID) {
      throw new Error('Unexpected tenant');
    }
    
    if (payload.azp && payload.azp !== BFF_CLIENT_ID) {
      throw new Error('Unexpected authorized party');
    }

    return {
      oid: payload.oid,
      name: payload.name,
      email: payload.preferred_username || payload.email,
      roles: payload.roles || [],
      groups: payload.groups || [],
      tenant: payload.tid,
    };
  } catch (error) {
    throw new Error(`Token validation failed: ${error.message}`);
  }
}

/**
 * Authentication middleware for Azure Container Apps Easy Auth
 */
export async function authMiddleware(req, res, next) {
  try {
    // In development, allow bypass if no Easy Auth headers
    if (process.env.NODE_ENV === 'development' && !req.headers['x-ms-client-principal']) {
      console.warn('⚠️  Development mode: Bypassing authentication');
      req.user = {
        oid: 'dev-user-id',
        name: 'Development User',
        email: '<EMAIL>',
        roles: [],
        groups: [],
        tenant: 'dev-tenant',
      };
      req.idToken = 'dev-token';
      return next();
    }

    // Extract ID token from Easy Auth headers
    const idToken = req.headers['x-ms-token-aad-id-token'] || req.headers['X-MS-TOKEN-AAD-ID-TOKEN'];
    
    if (!idToken) {
      return res.status(401).json({ 
        error: 'No ID token from Easy Auth',
        message: 'Authentication required. Please sign in through Azure AD.'
      });
    }

    // Validate the token
    const user = await validateIdToken(idToken);
    
    // Attach user info and token to request
    req.user = user;
    req.idToken = idToken;
    
    console.log(`✅ Authenticated user: ${user.name} (${user.email})`);
    next();
  } catch (error) {
    console.error('❌ Authentication error:', error.message);
    res.status(401).json({ 
      error: 'Authentication failed',
      message: error.message
    });
  }
}

/**
 * Optional middleware to check for specific roles
 * @param {string[]} requiredRoles - Array of required roles
 */
export function requireRoles(requiredRoles) {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const userRoles = req.user.roles || [];
    const hasRequiredRole = requiredRoles.some(role => userRoles.includes(role));

    if (!hasRequiredRole) {
      return res.status(403).json({ 
        error: 'Insufficient permissions',
        required: requiredRoles,
        current: userRoles
      });
    }

    next();
  };
}
