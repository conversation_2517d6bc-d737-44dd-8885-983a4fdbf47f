import express from "express"
import cors from "cors"
import helmet from "helmet"
import morgan from "morgan"
import dotenv from "dotenv"
import { fileURLToPath } from "url"
import { dirname, join } from "path"

// Import routes
import authRoutes from "./routes/auth.js"
import uploadRoutes from "./routes/upload.js"
import healthRoutes from "./routes/health.js"

// Import middleware
import { authMiddleware } from "./middleware/auth.js"
import { errorHandler } from "./middleware/errorHandler.js"

// Load environment variables
dotenv.config()

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

const app = express()
const PORT = process.env.PORT || 3001

// Security middleware
app.use(
  helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
      },
    },
  })
)

// CORS configuration
app.use(
  cors({
    origin: process.env.FRONTEND_URL || "http://localhost:5173",
    credentials: true,
  })
)

// Logging
app.use(morgan("combined"))

// Body parsing middleware
app.use(express.json({ limit: "10mb" }))
app.use(express.urlencoded({ extended: true, limit: "10mb" }))

// Health check (no auth required)
app.use("/health", healthRoutes)

// Authentication middleware for protected routes
app.use("/api", authMiddleware)

// Routes
app.use("/api/auth", authRoutes)
app.use("/api/upload", uploadRoutes)

// Error handling middleware
app.use(errorHandler)

// 404 handler
app.use((req, res) => {
  res.status(404).json({ error: "Route not found" })
})

app.listen(PORT, () => {
  console.log(`🚀 BFF Server running on port ${PORT}`)
  console.log(`📝 Environment: ${process.env.NODE_ENV || "development"}`)
  console.log(
    `🔗 Frontend URL: ${process.env.FRONTEND_URL || "http://localhost:5173"}`
  )
})

export default app
