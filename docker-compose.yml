version: '3.8'

services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:80"
    environment:
      - VITE_API_URL=http://localhost:3001
    depends_on:
      - backend
    networks:
      - leads-intake-network

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - PORT=3001
      - FRONTEND_URL=http://localhost:3000
      - AZURE_TENANT_ID=dev-tenant-id
      - BFF_CLIENT_ID=dev-client-id
      - LEADS_ADAPTER_API_URL=http://leads-adapter-api:8080
      - LOG_LEVEL=debug
    volumes:
      - ./backend/src:/app/src:ro
    networks:
      - leads-intake-network

  # Mock leads-adapter-api for development
  leads-adapter-api:
    image: nginx:alpine
    ports:
      - "8080:80"
    volumes:
      - ./mock-api/nginx.conf:/etc/nginx/conf.d/default.conf:ro
      - ./mock-api/responses:/usr/share/nginx/html:ro
    networks:
      - leads-intake-network

networks:
  leads-intake-network:
    driver: bridge

volumes:
  node_modules_backend:
  node_modules_frontend:
