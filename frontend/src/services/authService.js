const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001';

class AuthService {
  async getCurrentUser() {
    const response = await fetch(`${API_BASE_URL}/api/auth/me`, {
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Not authenticated');
    }

    const data = await response.json();
    return data.user;
  }

  async login() {
    // In production, this will redirect to Azure AD via Easy Auth
    // In development, we'll simulate the login
    if (import.meta.env.DEV) {
      console.log('Development mode: Simulating login');
      return;
    }

    window.location.href = `${API_BASE_URL}/api/auth/login`;
  }

  async logout() {
    const response = await fetch(`${API_BASE_URL}/api/auth/logout`, {
      method: 'POST',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Logout failed');
    }

    // In production, this will redirect to Azure AD logout
    if (!import.meta.env.DEV) {
      window.location.href = '/';
    }
  }
}

export const authService = new AuthService();
