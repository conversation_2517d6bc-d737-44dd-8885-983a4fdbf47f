/**
 * Service for processing leads data and communicating with leads-adapter-api
 */
class LeadsService {
  constructor() {
    this.leadsAdapterUrl = process.env.LEADS_ADAPTER_API_URL || 'http://leads-adapter-api:8080';
  }

  /**
   * Process leads data and send to leads-adapter-api
   * @param {string} content - The raw content to process
   * @param {Object} metadata - Additional metadata about the request
   * @returns {Object} - Processing result
   */
  async processLeadsData(content, metadata = {}) {
    try {
      // Parse and validate the content
      const parsedData = this.parseContent(content, metadata);
      
      // Prepare the payload for leads-adapter-api
      const payload = {
        data: parsedData,
        metadata: {
          source: metadata.source || 'file-upload',
          filename: metadata.filename,
          mimetype: metadata.mimetype,
          processedAt: new Date().toISOString(),
          processedBy: {
            userId: metadata.user?.oid,
            userName: metadata.user?.name,
            userEmail: metadata.user?.email,
          },
        },
      };

      // Send to leads-adapter-api
      const result = await this.sendToLeadsAdapter(payload, metadata.idToken);
      
      return {
        recordsProcessed: parsedData.length,
        adapterResponse: result,
      };
    } catch (error) {
      console.error('❌ Leads processing error:', error);
      throw new Error(`Failed to process leads data: ${error.message}`);
    }
  }

  /**
   * Parse content based on type and format
   * @param {string} content - Raw content
   * @param {Object} metadata - Content metadata
   * @returns {Array} - Parsed data array
   */
  parseContent(content, metadata) {
    if (!content || !content.trim()) {
      throw new Error('Content is empty');
    }

    const trimmedContent = content.trim();
    
    // Try to parse as JSON first
    if (this.isJsonContent(trimmedContent, metadata.mimetype)) {
      return this.parseJsonContent(trimmedContent);
    }
    
    // Try to parse as CSV
    if (this.isCsvContent(trimmedContent, metadata.mimetype)) {
      return this.parseCsvContent(trimmedContent);
    }
    
    // Default: treat as plain text with line-by-line parsing
    return this.parseTextContent(trimmedContent);
  }

  /**
   * Check if content is JSON
   */
  isJsonContent(content, mimetype) {
    return mimetype === 'application/json' || 
           (content.startsWith('{') || content.startsWith('['));
  }

  /**
   * Parse JSON content
   */
  parseJsonContent(content) {
    try {
      const parsed = JSON.parse(content);
      return Array.isArray(parsed) ? parsed : [parsed];
    } catch (error) {
      throw new Error('Invalid JSON format');
    }
  }

  /**
   * Check if content is CSV
   */
  isCsvContent(content, mimetype) {
    return mimetype === 'text/csv' || 
           mimetype === 'application/csv' ||
           content.includes(',') && content.includes('\n');
  }

  /**
   * Parse CSV content (basic implementation)
   */
  parseCsvContent(content) {
    const lines = content.split('\n').filter(line => line.trim());
    if (lines.length === 0) {
      throw new Error('CSV content is empty');
    }

    const headers = lines[0].split(',').map(h => h.trim());
    const data = [];

    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',').map(v => v.trim());
      if (values.length === headers.length) {
        const record = {};
        headers.forEach((header, index) => {
          record[header] = values[index];
        });
        data.push(record);
      }
    }

    return data;
  }

  /**
   * Parse plain text content
   */
  parseTextContent(content) {
    const lines = content.split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0);

    return lines.map((line, index) => ({
      id: index + 1,
      content: line,
      type: 'text-line',
    }));
  }

  /**
   * Send processed data to leads-adapter-api
   * @param {Object} payload - The data to send
   * @param {string} idToken - Azure AD ID token for authentication
   * @returns {Object} - API response
   */
  async sendToLeadsAdapter(payload, idToken) {
    try {
      console.log(`🔗 Sending data to leads-adapter-api: ${this.leadsAdapterUrl}`);
      
      const response = await fetch(`${this.leadsAdapterUrl}/api/leads/process`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${idToken}`,
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Leads adapter API error (${response.status}): ${errorText}`);
      }

      const result = await response.json();
      console.log('✅ Successfully sent to leads-adapter-api');
      return result;
    } catch (error) {
      // If leads-adapter-api is not available, simulate success for development
      if (process.env.NODE_ENV === 'development') {
        console.warn('⚠️  Development mode: Simulating leads-adapter-api response');
        return {
          status: 'success',
          message: 'Simulated processing (leads-adapter-api not available)',
          recordsReceived: payload.data.length,
          processedAt: new Date().toISOString(),
        };
      }
      
      throw error;
    }
  }
}

export const leadsService = new LeadsService();
