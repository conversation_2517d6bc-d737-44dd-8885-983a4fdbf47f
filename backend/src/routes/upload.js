import express from 'express';
import multer from 'multer';
import { asyncHand<PERSON> } from '../middleware/errorHandler.js';
import { leadsService } from '../services/leadsService.js';

const router = express.Router();

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    // Allow text files, CSV, and JSON
    const allowedMimes = [
      'text/plain',
      'text/csv',
      'application/json',
      'application/csv',
    ];
    
    if (allowedMimes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only .txt, .csv, and .json files are allowed.'));
    }
  },
});

/**
 * Upload and process file
 */
router.post('/file', upload.single('file'), asyncHandler(async (req, res) => {
  if (!req.file) {
    return res.status(400).json({ error: 'No file uploaded' });
  }

  const { originalname, mimetype, buffer } = req.file;
  const content = buffer.toString('utf-8');

  console.log(`📁 Processing file: ${originalname} (${mimetype})`);
  console.log(`👤 User: ${req.user.name} (${req.user.email})`);

  try {
    // Process the file content
    const result = await leadsService.processLeadsData(content, {
      filename: originalname,
      mimetype,
      user: req.user,
      idToken: req.idToken,
    });

    res.json({
      status: 'success',
      message: 'File processed successfully',
      filename: originalname,
      recordsProcessed: result.recordsProcessed,
      ...result,
    });
  } catch (error) {
    console.error('❌ File processing error:', error);
    res.status(500).json({
      error: 'File processing failed',
      message: error.message,
    });
  }
}));

/**
 * Process text input
 */
router.post('/text', asyncHandler(async (req, res) => {
  const { text } = req.body;

  if (!text || typeof text !== 'string' || !text.trim()) {
    return res.status(400).json({ error: 'Text content is required' });
  }

  console.log(`📝 Processing text input`);
  console.log(`👤 User: ${req.user.name} (${req.user.email})`);

  try {
    // Process the text content
    const result = await leadsService.processLeadsData(text, {
      source: 'text-input',
      user: req.user,
      idToken: req.idToken,
    });

    res.json({
      status: 'success',
      message: 'Text processed successfully',
      recordsProcessed: result.recordsProcessed,
      ...result,
    });
  } catch (error) {
    console.error('❌ Text processing error:', error);
    res.status(500).json({
      error: 'Text processing failed',
      message: error.message,
    });
  }
}));

export default router;
