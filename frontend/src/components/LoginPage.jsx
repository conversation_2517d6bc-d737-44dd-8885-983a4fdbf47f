import { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import './LoginPage.css';

function LoginPage() {
  const { login, error } = useAuth();
  const [isLoading, setIsLoading] = useState(false);

  const handleLogin = async () => {
    try {
      setIsLoading(true);
      await login();
    } catch (err) {
      console.error('Login failed:', err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="login-page">
      <div className="login-container">
        <div className="login-card">
          <div className="login-header">
            <h1>Welcome to Leads Intake</h1>
            <p>Sign in with your Office 365 account to continue</p>
          </div>

          {error && (
            <div className="error-message">
              <p>{error}</p>
            </div>
          )}

          <div className="login-content">
            <button 
              onClick={handleLogin}
              disabled={isLoading}
              className="login-button"
              type="button"
            >
              {isLoading ? (
                <>
                  <div className="spinner"></div>
                  Signing in...
                </>
              ) : (
                <>
                  <svg className="microsoft-icon" viewBox="0 0 24 24" width="20" height="20">
                    <path fill="#f25022" d="M1 1h10v10H1z"/>
                    <path fill="#00a4ef" d="M13 1h10v10H13z"/>
                    <path fill="#7fba00" d="M1 13h10v10H1z"/>
                    <path fill="#ffb900" d="M13 13h10v10H13z"/>
                  </svg>
                  Sign in with Microsoft
                </>
              )}
            </button>
          </div>

          <div className="login-footer">
            <p>
              This application uses Azure Active Directory for secure authentication.
              You'll be redirected to Microsoft's login page.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default LoginPage;
