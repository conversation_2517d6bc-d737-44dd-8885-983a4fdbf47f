.login-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem;
}

.login-container {
  width: 100%;
  max-width: 400px;
}

.login-card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.login-header {
  text-align: center;
  margin-bottom: 2rem;
}

.login-header h1 {
  margin: 0 0 0.5rem 0;
  color: #1f2937;
  font-size: 1.5rem;
  font-weight: 600;
}

.login-header p {
  margin: 0;
  color: #6b7280;
  font-size: 0.9rem;
}

.login-content {
  margin-bottom: 1.5rem;
}

.login-button {
  width: 100%;
  background-color: #0078d4;
  color: white;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: background-color 0.2s;
}

.login-button:hover:not(:disabled) {
  background-color: #106ebe;
}

.login-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.microsoft-icon {
  flex-shrink: 0;
}

.login-footer {
  text-align: center;
  color: #6b7280;
  font-size: 0.8rem;
  line-height: 1.4;
}

.error-message {
  margin-bottom: 1rem;
}

@media (max-width: 480px) {
  .login-page {
    padding: 1rem;
  }
  
  .login-card {
    padding: 1.5rem;
  }
}
